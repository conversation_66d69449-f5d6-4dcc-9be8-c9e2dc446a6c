<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Skills & Stack - <PERSON></title>
    <link rel="stylesheet" href="style.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <h2>
            <a href="index.html" style="text-decoration: none; color: inherit"
              >Daniel<span class="accent">.</span></a
            >
          </h2>
        </div>
        <ul class="nav-menu" id="nav-menu">
          <li class="nav-item">
            <a href="index.html" class="nav-link">Home</a>
          </li>
          <li class="nav-item">
            <a href="about.html" class="nav-link">About</a>
          </li>
          <li class="nav-item">
            <a href="stack.html" class="nav-link active">Skills</a>
          </li>
          <li class="nav-item">
            <a href="index.html#projects" class="nav-link">Projects</a>
          </li>
          <li class="nav-item">
            <a href="contact.html" class="nav-link">Contact</a>
          </li>
        </ul>
        <div class="hamburger" id="hamburger">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </div>
      </div>
    </nav>

    <!-- Skills Hero Section -->
    <section class="skills-hero">
      <div class="container">
        <div class="skills-hero-content">
          <h1>Skills & Technologies</h1>
          <p>My technical expertise and development stack</p>
        </div>
      </div>
    </section>

    <!-- Detailed Skills Section -->
    <section class="skills-detailed">
      <div class="container">
        <div class="skills-intro">
          <h2>Technical Proficiency</h2>
          <p>
            I specialize in modern web development technologies, focusing on
            creating scalable, maintainable, and user-friendly applications.
            Here's a comprehensive overview of my technical skills and
            experience levels.
          </p>
        </div>

        <div class="skills-grid-detailed">
          <!-- Frontend Skills -->
          <div class="skill-group">
            <h3><i class="fas fa-laptop-code"></i> Frontend Development</h3>
            <div class="skill-items">
              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">HTML5</span>
                  <span class="skill-percentage">95%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="95"></div>
                </div>
                <p>Semantic markup, accessibility, modern HTML standards</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">CSS3</span>
                  <span class="skill-percentage">90%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="90"></div>
                </div>
                <p>
                  Flexbox, Grid, animations, responsive design, preprocessors
                </p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">JavaScript</span>
                  <span class="skill-percentage">85%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="85"></div>
                </div>
                <p>ES6+, DOM manipulation, async/await, modern frameworks</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">Responsive Design</span>
                  <span class="skill-percentage">88%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="88"></div>
                </div>
                <p>Mobile-first approach, cross-browser compatibility</p>
              </div>
            </div>
          </div>

          <!-- Backend Skills -->
          <div class="skill-group">
            <h3><i class="fas fa-server"></i> Backend Development</h3>
            <div class="skill-items">
              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">Node.js</span>
                  <span class="skill-percentage">80%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="80"></div>
                </div>
                <p>Server-side JavaScript, npm ecosystem, async programming</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">Express.js</span>
                  <span class="skill-percentage">75%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="75"></div>
                </div>
                <p>RESTful APIs, middleware, routing, authentication</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">API Development</span>
                  <span class="skill-percentage">78%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="78"></div>
                </div>
                <p>REST APIs, JSON handling, API documentation</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">Web Security</span>
                  <span class="skill-percentage">70%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="70"></div>
                </div>
                <p>Authentication, authorization, data validation</p>
              </div>
            </div>
          </div>

          <!-- Database Skills -->
          <div class="skill-group">
            <h3><i class="fas fa-database"></i> Database & Storage</h3>
            <div class="skill-items">
              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">SQL Databases</span>
                  <span class="skill-percentage">72%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="72"></div>
                </div>
                <p>MySQL, PostgreSQL, database design, queries</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">NoSQL Databases</span>
                  <span class="skill-percentage">68%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="68"></div>
                </div>
                <p>MongoDB, document-based storage, data modeling</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">Data Management</span>
                  <span class="skill-percentage">75%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="75"></div>
                </div>
                <p>CRUD operations, data relationships, optimization</p>
              </div>
            </div>
          </div>

          <!-- Tools & Workflow -->
          <div class="skill-group">
            <h3><i class="fas fa-tools"></i> Tools & Workflow</h3>
            <div class="skill-items">
              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">Git & GitHub</span>
                  <span class="skill-percentage">82%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="82"></div>
                </div>
                <p>Version control, branching, collaboration, CI/CD</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">Testing</span>
                  <span class="skill-percentage">65%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="65"></div>
                </div>
                <p>Unit testing, integration testing, debugging</p>
              </div>

              <div class="skill-item">
                <div class="skill-header">
                  <span class="skill-name">Performance Optimization</span>
                  <span class="skill-percentage">70%</span>
                </div>
                <div class="skill-bar-container">
                  <div class="skill-bar" data-level="70"></div>
                </div>
                <p>Code optimization, caching, load time improvement</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Learning & Goals -->
    <section class="learning-goals">
      <div class="container">
        <div class="learning-content">
          <div class="currently-learning">
            <h2><i class="fas fa-graduation-cap"></i> Currently Learning</h2>
            <div class="learning-items">
              <div class="learning-item">
                <h4>React.js</h4>
                <p>Modern frontend framework for building user interfaces</p>
              </div>
              <div class="learning-item">
                <h4>TypeScript</h4>
                <p>Adding type safety to JavaScript applications</p>
              </div>
              <div class="learning-item">
                <h4>Docker</h4>
                <p>Containerization for consistent development environments</p>
              </div>
            </div>
          </div>

          <div class="future-goals">
            <h2><i class="fas fa-target"></i> Future Goals</h2>
            <div class="goals-items">
              <div class="goal-item">
                <h4>Cloud Technologies</h4>
                <p>AWS, Azure, and cloud deployment strategies</p>
              </div>
              <div class="goal-item">
                <h4>Mobile Development</h4>
                <p>React Native for cross-platform mobile apps</p>
              </div>
              <div class="goal-item">
                <h4>DevOps</h4>
                <p>CI/CD pipelines and infrastructure automation</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>Daniel Omotehinse</h3>
            <p>
              Full Stack Developer passionate about creating amazing web
              experiences.
            </p>
            <div class="footer-social">
              <a
                href="https://web.facebook.com/daniel.omotehinse.3"
                target="_blank"
                class="social-link"
              >
                <i class="fab fa-facebook-f"></i>
              </a>
              <a
                href="https://twitter.com/Dahnilo_99"
                target="_blank"
                class="social-link"
              >
                <i class="fab fa-twitter"></i>
              </a>
              <a
                href="https://www.instagram.com/omotehinse_99/"
                target="_blank"
                class="social-link"
              >
                <i class="fab fa-instagram"></i>
              </a>
              <a
                href="https://github.com/danyboy99"
                target="_blank"
                class="social-link"
              >
                <i class="fab fa-github"></i>
              </a>
            </div>
          </div>
          <div class="footer-section">
            <h4>Quick Links</h4>
            <ul>
              <li><a href="index.html">Home</a></li>
              <li><a href="about.html">About</a></li>
              <li><a href="stack.html">Skills</a></li>
              <li><a href="index.html#projects">Projects</a></li>
              <li><a href="contact.html">Contact</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Contact Info</h4>
            <div class="footer-contact">
              <p><i class="fas fa-phone"></i> +234 ************</p>
              <p><i class="fas fa-envelope"></i> <EMAIL></p>
              <p><i class="fas fa-map-marker-alt"></i> Lagos, Nigeria</p>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Daniel Omotehinse. All rights reserved.</p>
          <p class="footer-quote">
            "Anything is possible, you just have to believe and try"
          </p>
        </div>
      </div>
    </footer>

    <script src="script.js"></script>
  </body>
</html>
