/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #ffffff;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* ===== TYPOGRAPHY ===== */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

.accent {
  color: #6366f1;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.btn-secondary {
  background: transparent;
  color: #6366f1;
  border: 2px solid #6366f1;
}

.btn-secondary:hover {
  background: #6366f1;
  color: white;
  transform: translateY(-2px);
}
/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.nav-logo h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: #6366f1;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #6366f1;
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background: #333;
  transition: all 0.3s ease;
  border-radius: 3px;
}
/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%236366f1" stop-opacity="0.1"/><stop offset="100%" stop-color="%236366f1" stop-opacity="0"/></radialGradient></defs><circle cx="50%" cy="50%" r="50%" fill="url(%23a)"/></svg>');
  opacity: 0.5;
}

.hero-container {
  position: relative;
  z-index: 2;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #6366f1;
  margin-bottom: 1.5rem;
  min-height: 60px;
}

.hero-description {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.7;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: white;
  border-radius: 50%;
  color: #6366f1;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.social-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  background: #6366f1;
  color: white;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-container {
  position: relative;
  width: 400px;
  height: 400px;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 8px solid white;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.image-overlay {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 3px solid #6366f1;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}
/* ===== SECTION STYLES ===== */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1e293b;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about-section {
  padding: 100px 0;
  background: white;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  align-items: center;
}

.about-image {
  position: relative;
}

.about-img {
  width: 100%;
  max-width: 400px;
  height: 500px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.about-image-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border: 3px solid #6366f1;
  border-radius: 20px;
  opacity: 0.3;
}

.about-name {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.about-role {
  font-size: 1.2rem;
  color: #6366f1;
  font-weight: 600;
  margin-bottom: 2rem;
}

.about-description p {
  margin-bottom: 1.5rem;
  color: #64748b;
  line-height: 1.7;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin: 2rem 0;
  padding: 2rem 0;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
}

.stat {
  text-align: center;
}

.stat h4 {
  font-size: 2rem;
  font-weight: 700;
  color: #6366f1;
  margin-bottom: 0.5rem;
}

.stat p {
  color: #64748b;
  font-weight: 500;
}
/* ===== SKILLS SECTION ===== */
.skills-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.skill-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.skill-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.skill-icon {
  font-size: 3rem;
  color: #6366f1;
  margin-bottom: 1rem;
}

.skill-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1e293b;
}

.skill-card p {
  color: #64748b;
  margin-bottom: 1.5rem;
}

.skill-level {
  background: #e2e8f0;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.skill-bar {
  height: 100%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 4px;
  transition: width 2s ease;
  width: 0;
}

.skills-additional {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.skills-additional h3 {
  font-size: 1.8rem;
  margin-bottom: 2rem;
  color: #1e293b;
}

.additional-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.skill-tag {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
}
/* ===== PROJECTS SECTION ===== */
.projects-section {
  padding: 100px 0;
  background: white;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.project-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.project-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .project-img {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(99, 102, 241, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-links {
  display: flex;
  gap: 1rem;
}

.project-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: white;
  border-radius: 50%;
  color: #6366f1;
  text-decoration: none;
  transition: all 0.3s ease;
}

.project-link:hover {
  transform: scale(1.1);
  background: #6366f1;
  color: white;
}

.project-content {
  padding: 2rem;
}

.project-content h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1e293b;
}

.project-content p {
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  background: #f1f5f9;
  color: #6366f1;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* ===== FEATURED PROJECTS ===== */
.project-card.featured {
  border: 2px solid #6366f1;
  position: relative;
  overflow: hidden;
}

.project-card.featured::before {
  content: "FEATURED";
  position: absolute;
  top: 15px;
  right: -30px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 0.3rem 2rem;
  font-size: 0.7rem;
  font-weight: 600;
  letter-spacing: 1px;
  transform: rotate(45deg);
  z-index: 10;
}

.project-card.featured:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(99, 102, 241, 0.2);
}

.projects-cta {
  text-align: center;
  margin-top: 3rem;
}

.additional-projects {
  animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* ===== CERTIFICATES SECTION ===== */
.certificates-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.certificates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.certificate-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.certificate-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.certificate-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.cert-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.certificate-card:hover .cert-img {
  transform: scale(1.05);
}

.certificate-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(99, 102, 241, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.certificate-card:hover .certificate-overlay {
  opacity: 1;
}

.certificate-info {
  text-align: center;
  color: white;
  padding: 2rem;
}

.certificate-info h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.certificate-info p {
  margin-bottom: 1rem;
  opacity: 0.9;
}

.view-cert-btn {
  background: white;
  color: #6366f1;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-cert-btn:hover {
  transform: scale(1.05);
}
/* ===== CONTACT SECTION ===== */
.contact-section {
  padding: 100px 0;
  background: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 3rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.contact-details h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.contact-details a {
  color: #6366f1;
  text-decoration: none;
  display: block;
  margin-bottom: 0.3rem;
}

.contact-details a:hover {
  text-decoration: underline;
}

.contact-social {
  margin-top: 2rem;
}

.contact-social h3 {
  margin-bottom: 1rem;
  color: #1e293b;
}

.contact-form {
  background: #f8fafc;
  padding: 2rem;
  border-radius: 20px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #6366f1;
}

.contact-map {
  margin: 3rem 0;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}
/* ===== FOOTER ===== */
.footer {
  background: #1e293b;
  color: white;
  padding: 4rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: white;
}

.footer-section h4 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #e2e8f0;
}

.footer-section p {
  color: #94a3b8;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section ul li a {
  color: #94a3b8;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #6366f1;
}

.footer-social {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.footer-contact p {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #94a3b8;
}

.footer-bottom {
  border-top: 1px solid #334155;
  padding-top: 2rem;
  text-align: center;
}

.footer-bottom p {
  color: #94a3b8;
  margin-bottom: 0.5rem;
}

.footer-quote {
  font-style: italic;
  color: #6366f1;
}
/* ===== MODAL ===== */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  margin: 5% auto;
  padding: 0;
  width: 90%;
  max-width: 800px;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.close-modal {
  position: absolute;
  top: 20px;
  right: 30px;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  z-index: 2001;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.close-modal:hover {
  background: rgba(0, 0, 0, 0.8);
}

.modal-img {
  width: 100%;
  height: auto;
  display: block;
}
/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 1rem 0;
  }

  .hamburger.active .bar:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .hamburger.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .about-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .about-name {
    font-size: 1.5rem;
  }

  .about-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat h4 {
    font-size: 1.5rem;
  }

  .skill-card {
    padding: 1.5rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .certificates-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }

  .image-container {
    width: 300px;
    height: 300px;
  }

  .about-img {
    max-width: 300px;
    height: 400px;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .modal-content {
    width: 95%;
    margin: 10% auto;
  }
}
/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease forwards;
}

/* ===== UTILITY CLASSES ===== */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 1rem;
}
.mb-2 {
  margin-bottom: 2rem;
}
.mb-3 {
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 1rem;
}
.mt-2 {
  margin-top: 2rem;
}
.mt-3 {
  margin-top: 3rem;
}

/* ===== SCROLL ANIMATIONS ===== */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* ===== LOADING STATES ===== */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* ===== FOCUS STYLES ===== */
*:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* ===== SELECTION STYLES ===== */
::selection {
  background: #6366f1;
  color: white;
}

/* ===== ABOUT PAGE STYLES ===== */
.about-hero {
  padding: 150px 0 100px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  text-align: center;
}

.about-hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.about-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.about-detailed {
  padding: 100px 0;
  background: white;
}

.about-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  align-items: start;
}

.about-images {
  position: relative;
}

.main-image {
  position: relative;
  z-index: 2;
}

.about-main-img {
  width: 100%;
  max-width: 400px;
  height: 500px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.secondary-image {
  position: absolute;
  top: 50px;
  right: -50px;
  z-index: 1;
}

.about-secondary-img {
  width: 200px;
  height: 250px;
  object-fit: cover;
  border-radius: 15px;
  border: 5px solid white;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.about-content-detailed h2 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.about-content-detailed h3 {
  font-size: 1.3rem;
  color: #6366f1;
  margin-bottom: 2rem;
}

.about-story,
.about-interests,
.about-philosophy {
  margin-bottom: 2.5rem;
}

.about-content-detailed h4 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1e293b;
}

.about-content-detailed p {
  color: #64748b;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.about-content-detailed blockquote {
  background: #f8fafc;
  border-left: 4px solid #6366f1;
  padding: 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
  font-size: 1.1rem;
  color: #1e293b;
}

.skills-overview {
  padding: 100px 0;
  background: #f8fafc;
}

.skills-overview h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1e293b;
}

.skills-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.skill-category {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.skill-category h3 {
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.skill-category h3 i {
  color: #6366f1;
}

.skill-category ul {
  list-style: none;
}

.skill-category li {
  padding: 0.5rem 0;
  color: #64748b;
  position: relative;
  padding-left: 1.5rem;
}

.skill-category li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #6366f1;
  font-weight: bold;
}

.experience-timeline {
  padding: 100px 0;
  background: white;
}

.experience-timeline h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1e293b;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #6366f1;
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 3rem;
  display: flex;
  align-items: center;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-date {
  background: #6366f1;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  white-space: nowrap;
  position: relative;
  z-index: 2;
}

.timeline-content {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin: 0 2rem;
  flex: 1;
}

.timeline-content h3 {
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.timeline-content p {
  color: #64748b;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .about-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .secondary-image {
    position: static;
    margin-top: 2rem;
  }

  .about-hero h1 {
    font-size: 2.5rem;
  }

  .skills-categories {
    grid-template-columns: 1fr;
  }

  .timeline::before {
    left: 20px;
  }

  .timeline-item {
    flex-direction: column !important;
    align-items: flex-start;
    padding-left: 3rem;
  }

  .timeline-item:nth-child(even) {
    flex-direction: column !important;
  }

  .timeline-date {
    margin-bottom: 1rem;
  }

  .timeline-content {
    margin: 0;
  }
}

/* ===== STACK PAGE STYLES ===== */
.skills-hero {
  padding: 150px 0 100px;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  text-align: center;
}

.skills-hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.skills-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.skills-detailed {
  padding: 100px 0;
  background: white;
}

.skills-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.skills-intro h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #1e293b;
}

.skills-intro p {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.7;
}

.skills-grid-detailed {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 3rem;
}

.skill-group {
  background: #f8fafc;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.skill-group h3 {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.skill-group h3 i {
  color: #6366f1;
  font-size: 1.3rem;
}

.skill-items {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.skill-item {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.skill-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 1.1rem;
}

.skill-percentage {
  font-weight: 600;
  color: #6366f1;
  font-size: 0.9rem;
}

.skill-bar-container {
  background: #e2e8f0;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.8rem;
}

.skill-item p {
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
}

.learning-goals {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.learning-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.currently-learning h2,
.future-goals h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.currently-learning h2 i,
.future-goals h2 i {
  color: #6366f1;
}

.learning-items,
.goals-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.learning-item,
.goal-item {
  background: white;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.learning-item:hover,
.goal-item:hover {
  transform: translateY(-5px);
}

.learning-item h4,
.goal-item h4 {
  color: #1e293b;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.learning-item p,
.goal-item p {
  color: #64748b;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .skills-hero h1 {
    font-size: 2.5rem;
  }

  .skills-grid-detailed {
    grid-template-columns: 1fr;
  }

  .learning-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .skill-group {
    padding: 2rem;
  }

  .currently-learning h2,
  .future-goals h2 {
    font-size: 1.8rem;
  }
}

/* ===== CONTACT PAGE STYLES ===== */
.contact-hero {
  padding: 150px 0 100px;
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  color: white;
  text-align: center;
}

.contact-hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.contact-hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.contact-main {
  padding: 100px 0;
  background: white;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info-detailed h2 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
  color: #1e293b;
}

.contact-info-detailed > p {
  color: #64748b;
  line-height: 1.7;
  margin-bottom: 3rem;
}

.contact-methods {
  margin-bottom: 3rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.contact-method:hover {
  transform: translateY(-3px);
}

.contact-method .contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.contact-method .contact-details h3 {
  color: #1e293b;
  margin-bottom: 0.3rem;
  font-size: 1.1rem;
}

.contact-method .contact-details p {
  color: #64748b;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.contact-method .contact-details a,
.contact-method .contact-details span {
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
  display: block;
  margin-bottom: 0.2rem;
}

.contact-method .contact-details a:hover {
  text-decoration: underline;
}

.social-section h3 {
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.social-section p {
  color: #64748b;
  margin-bottom: 1rem;
}

.social-links-detailed {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.social-link-detailed {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  text-decoration: none;
  color: #64748b;
  transition: all 0.3s ease;
}

.social-link-detailed:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-link-detailed.facebook:hover {
  border-color: #1877f2;
  color: #1877f2;
}

.social-link-detailed.twitter:hover {
  border-color: #1da1f2;
  color: #1da1f2;
}

.social-link-detailed.instagram:hover {
  border-color: #e4405f;
  color: #e4405f;
}

.social-link-detailed.github:hover {
  border-color: #333;
  color: #333;
}

.contact-form-detailed {
  background: #f8fafc;
  padding: 2.5rem;
  border-radius: 20px;
}

.form-container h2 {
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.form-container p {
  color: #64748b;
  margin-bottom: 2rem;
}

.contact-form-main {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #1e293b;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #6366f1;
}

.checkbox-group {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
  cursor: pointer;
  color: #64748b;
  font-size: 0.9rem;
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.map-section {
  padding: 100px 0;
  background: #f8fafc;
  text-align: center;
}

.map-section h2 {
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.map-section p {
  color: #64748b;
  margin-bottom: 2rem;
}

.map-container {
  max-width: 1000px;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  overflow: hidden;
}

.faq-section {
  padding: 100px 0;
  background: white;
}

.faq-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #1e293b;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.faq-item {
  background: #f8fafc;
  padding: 2rem;
  border-radius: 15px;
  border-left: 4px solid #6366f1;
}

.faq-item h3 {
  color: #1e293b;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.faq-item p {
  color: #64748b;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .contact-hero h1 {
    font-size: 2.5rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .social-links-detailed {
    grid-template-columns: 1fr;
  }

  .contact-form-detailed {
    padding: 2rem;
  }

  .faq-grid {
    grid-template-columns: 1fr;
  }
}
